<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-10" y="-10" width="60" height="60"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_0_417_302_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="10">
<rect x="0.5" y="0.5" width="39" height="39" rx="19.5" fill="white" fill-opacity="0.1"/>
<rect x="0.5" y="0.5" width="39" height="39" rx="19.5" stroke="url(#paint0_radial_417_302)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13 20.0063C13 19.741 13.1054 19.4867 13.2929 19.2991C13.4804 19.1116 13.7348 19.0063 14 19.0063H26C26.2652 19.0063 26.5196 19.1116 26.7071 19.2991C26.8946 19.4867 27 19.741 27 20.0063C27 20.2715 26.8946 20.5258 26.7071 20.7134C26.5196 20.9009 26.2652 21.0063 26 21.0063H14C13.7348 21.0063 13.4804 20.9009 13.2929 20.7134C13.1054 20.5258 13 20.2715 13 20.0063Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.707 25.7133C19.5195 25.9007 19.2652 26.006 19 26.006C18.7349 26.006 18.4806 25.9007 18.293 25.7133L13.293 20.7133C13.1056 20.5257 13.0002 20.2714 13.0002 20.0063C13.0002 19.7411 13.1056 19.4868 13.293 19.2993L18.293 14.2993C18.3853 14.2037 18.4956 14.1276 18.6176 14.0752C18.7396 14.0227 18.8709 13.9952 19.0036 13.994C19.1364 13.9928 19.2681 14.0182 19.391 14.0684C19.5139 14.1187 19.6255 14.193 19.7194 14.2869C19.8133 14.3808 19.8876 14.4924 19.9379 14.6153C19.9881 14.7382 20.0134 14.8699 20.0123 15.0027C20.0111 15.1354 19.9835 15.2667 19.9311 15.3887C19.8787 15.5107 19.8025 15.621 19.707 15.7133L15.414 20.0063L19.707 24.2993C19.8945 24.4868 19.9998 24.7411 19.9998 25.0063C19.9998 25.2714 19.8945 25.5257 19.707 25.7133Z" fill="white"/>
</g>
<defs>
<clipPath id="bgblur_0_417_302_clip_path" transform="translate(10 10)"><rect x="0.5" y="0.5" width="39" height="39" rx="19.5"/>
</clipPath><radialGradient id="paint0_radial_417_302" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9 5.5) rotate(45) scale(39.598 30.7707)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
