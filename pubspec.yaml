name: zride
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.1
  dio: ^5.8.0+1
  path_provider: ^2.1.5
  hive: ^2.2.3
  connectivity_plus: ^6.1.4
  one_context: ^4.1.0
  animated_splash_screen: ^1.3.0
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  lottie: ^3.3.1
  flutter_svg: ^2.2.0
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  shimmer: ^3.0.0
  validators: ^3.0.0
  country_picker: ^2.0.27
  equatable: ^2.0.7
  dotted_line: ^3.2.3
  smooth_page_indicator: ^1.2.1
  extended_masked_text: ^3.0.1
  toastification: ^3.0.3
  flutter_native_splash: ^2.4.6
  dotted_border: ^3.1.0
  flutter_swipe_button: ^2.1.3
  pretty_dio_logger: ^1.4.0
  flutter_dotenv: ^5.2.1
  pinput: ^5.0.1
  geolocator: ^14.0.2
  google_maps_flutter: ^2.12.3
  permission_handler: ^12.0.1
  loading_indicator: ^3.1.1
  confetti: ^0.8.0
  intl: ^0.20.2
  timezone: ^0.10.1
  geocoding: ^4.0.0
  keyboard_actions: ^4.2.0

flutter_gen:
  line_length: 160
  integrations:
    flutter_svg: true
    lottie: true
  assets:
    enabled: true
  output: lib/core/generated

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.6.0
  intl_utils: ^2.8.11
  flutter_gen_runner: ^5.11.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

flutter_intl:
  enabled: true
  class_name: Lang
  main_locale: en
  arb_dir: lib/core/l10n 
  output_dir: lib/core/generated

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/images/pngs/
    - assets/images/pngs/authentication/
    - assets/images/pngs/login/
    - assets/images/pngs/onboarding/
    - assets/images/pngs/other/
    - assets/images/svgs/
    - assets/images/svgs/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
  - family: InstrumentSans
    fonts:
      - asset: assets/fonts/InstrumentSans-Bold.ttf
        weight: 500
      - asset: assets/fonts/InstrumentSans-Medium.ttf
        weight: 400
      - asset: assets/fonts/InstrumentSans-Regular.ttf
        weight: 300
      - asset: assets/fonts/InstrumentSans-SemiBold.ttf
        weight: 600  
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_native_splash:
  color: "#FFFFFF"
  android: true 
  ios: true 