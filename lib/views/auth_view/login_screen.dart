import 'package:zride/viewmodels/auth_bloc/auth_bloc.dart';
import 'package:zride/viewmodels/auth_bloc/auth_event.dart';
import 'package:zride/viewmodels/auth_bloc/auth_state.dart';
import 'package:zride/widgets/common_widget/common_textfields.dart';
import '../../core/utils/app_exports.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  static Widget builder(BuildContext context) {
    return const LoginScreen();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.light),
    );
    late TabController _tabController;
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final mobileController = TextEditingController();
    final passwordController = TextEditingController();

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is RegisterSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Registration Successful')));
        } else if (state is OtpSentSuccess) {
          NavigatorService.pushNamed(AppRoutes.otpScreen);
        } else if (state is LoginError) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              Positioned(child: CustomImageView(imagePath: Assets.images.pngs.login.pngLoginBg.path)),
              Padding(
                padding: EdgeInsets.only(left: 20.0, right: 20.0, top: 88.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Go Ahead And Set Up Your Account",
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimary),
                    ),
                    buildSizedBoxH(16),
                    Text(
                      "Welcome back! Ready to book your next ride? We'll get you moving in seconds.",
                      style: Theme.of(
                        context,
                      ).textTheme.labelLarge?.copyWith(color: Theme.of(context).colorScheme.secondary),
                    ),
                  ],
                ),
              ),
              _buildLoginFields(context, nameController, emailController, mobileController, passwordController),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoginFields(
    BuildContext context,
    TextEditingController nameController,
    TextEditingController emailController,
    TextEditingController mobileController,
    TextEditingController passwordController,
  ) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.7,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
        ),
        child: DefaultTabController(
          length: 2,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Container(
                  height: 52.h,
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondaryContainer,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: TabBar(
                    tabs: [
                      Tab(
                        child: Text(
                          "Register",
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w500),
                        ),
                      ),
                      Tab(
                        child: Text(
                          "Log In",
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ],
                    indicator: BoxDecoration(
                      color: Theme.of(context).colorScheme.onPrimary,
                      borderRadius: BorderRadius.circular(26),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    indicatorPadding: EdgeInsets.zero,
                    dividerColor: Colors.transparent,
                    labelColor: Theme.of(context).customColors.hinttextcolor,
                    unselectedLabelColor: Theme.of(context).colorScheme.onSecondary,
                    labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    unselectedLabelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    splashFactory: NoSplash.splashFactory,
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                  ),
                ),
                buildSizedBoxH(20),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildRegisterForm(
                        context,
                        nameController,
                        emailController,
                        mobileController,
                        passwordController,
                      ),
                      _buildLoginForm(context, emailController),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRegisterForm(
    BuildContext context,
    TextEditingController nameController,
    TextEditingController emailController,
    TextEditingController mobileController,
    TextEditingController passwordController,
  ) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField(
            controller: nameController,
            imagePath: Assets.images.svgs.icons.svgIcContact.path,
            labelText: 'Name',
            hintText: 'Enter Your Name',
            type: InputType.text,
            textInputAction: TextInputAction.next,
            onChanged: (_) {},
          ),
          buildSizedBoxH(12),
          _buildTextField(
            controller: emailController,
            imagePath: Assets.images.svgs.icons.svgIcEmail.path,
            labelText: 'Email Address',
            hintText: 'Enter Your Email',
            type: InputType.email,
            textInputAction: TextInputAction.next,
            onChanged: (_) {},
          ),
          buildSizedBoxH(12),
          _buildTextField(
            controller: mobileController,
            imagePath: Assets.images.svgs.icons.svgIcCall.path,
            labelText: 'Mobile Number',
            hintText: 'Enter Mobile Number',
            type: InputType.phoneNumber,
            textInputAction: TextInputAction.next,
            onChanged: (_) {},
          ),
          buildSizedBoxH(12),
          _buildTextField(
            controller: passwordController,
            imagePath: Assets.images.svgs.icons.svgIcPassword.path,
            labelText: 'Password',
            hintText: 'Enter Password',
            type: InputType.password,
            obscure: true,
            textInputAction: TextInputAction.done,
            onChanged: (_) {},
          ),
          buildSizedBoxH(58),
          CustomElevatedButton(
            text: "Register",
            onPressed: () {
              context.read<AuthBloc>().add(
                RegisterUserEvent(
                  name: nameController.text.trim(),
                  address: emailController.text.trim(),
                  mobile: mobileController.text.trim(),
                  password: passwordController.text.trim(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm(BuildContext context, TextEditingController emailController) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField(
            controller: emailController,
            imagePath: Assets.images.svgs.icons.svgIcCredentials.path,
            labelText: 'Email Address / Mobile Number',
            hintText: 'Enter your credentials',
            type: InputType.email,
            textInputAction: TextInputAction.done,
            onChanged: (_) {},
          ),
          buildSizedBoxH(40),
          CustomElevatedButton(
            text: "Log In",
            onPressed: () {
              context.read<AuthBloc>().add(SendOtpEvent(email: emailController.text.trim()));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String imagePath,
    required String labelText,
    required String hintText,
    required InputType? type,
    required TextInputAction textInputAction,
    required Function(String)? onChanged,
    bool obscure = false,
  }) {
    return CommonTextfields(
      controller: controller,
      onChanged: onChanged,
      imagePath: imagePath,
      labelText: labelText,
      hintText: hintText,
      type: type,
      obscureText: obscure,
      textInputAction: textInputAction,
    );
  }
}
