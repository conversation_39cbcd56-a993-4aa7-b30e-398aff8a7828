import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/home_bloc/home_bloc.dart';
import 'package:zride/viewmodels/home_bloc/home_event.dart';
import 'package:zride/viewmodels/home_bloc/home_state.dart';
import 'package:zride/widgets/common_widget/common_location_textfield.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  static Widget builder(BuildContext context) {
    return const HomeScreen();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final bloc = context.read<HomeBloc>();

        final List<Map<String, dynamic>> navItems = [
          {'icon': Assets.images.svgs.icons.svgIcHome.path, 'label': 'Home'},
          {
            'icon': Assets.images.svgs.icons.svgIcWallet.path,
            'label': 'Wallet',
          },
          {'icon': Assets.images.svgs.icons.svgIcRide.path, 'label': 'Ride'},
          {
            'icon': Assets.images.svgs.icons.svgIcSetting.path,
            'label': 'Setting',
          },
        ];

        return Scaffold(
          backgroundColor: const Color(0xFFFBF6F6),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    buildSizedBoxH(20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome, John!',
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            buildSizedBoxH(15),
                            Text(
                              'Where would you like to go?',
                              style: Theme.of(context).textTheme.labelLarge
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSecondary,
                                  ),
                            ),
                          ],
                        ),
                        CircleAvatar(
                          backgroundColor: Colors.white,
                          child: CustomImageView(
                            imagePath:
                                Assets.images.svgs.icons.svgIcNotification.path,
                          ),
                        ),
                      ],
                    ),
                    buildSizedBoxH(20),
                    TextField(
                      onChanged: (value) {
                        bloc.add(UpdateSearchQuery(value));
                      },
                      style: TextStyle(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSecondaryContainer,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Where do you want to go?',
                        hintStyle: TextStyle(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSecondaryContainer,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSecondaryContainer,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(50),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(50),
                          borderSide: const BorderSide(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                        fillColor: Colors.white,
                        filled: true,
                      ),
                    ),
                    buildSizedBoxH(20),
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            LocationTextField(
                              value: state.startLocation,
                              hint: 'Start Location',
                              onChanged: (value) {
                                bloc.add(UpdateStartLocation(value));
                              },
                            ),
                            _buildSwapButton(context),
                            LocationTextField(
                              value: state.endLocation,
                              hint: 'Your Location',
                              onChanged: (value) {
                                bloc.add(UpdateEndLocation(value));
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    buildSizedBoxH(20),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Last Rides',
                                style: Theme.of(context).textTheme.labelLarge
                                    ?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(
                                        context,
                                      ).customColors.hinttextcolor,
                                    ),
                              ),
                              Text(
                                'See all',
                                style: Theme.of(context).textTheme.labelLarge
                                    ?.copyWith(
                                      color: Theme.of(
                                        context,
                                      ).colorScheme.onSecondaryContainer,
                                    ),
                              ),
                            ],
                          ),
                          buildSizedBoxH(50),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.only(bottom: 4.0.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(navItems.length, (index) {
                final isSelected = state.selectedBottomNavIndex == index;

                return GestureDetector(
                  onTap: () {
                    bloc.add(ChangeBottomNavIndex(index));
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 8,
                        child: isSelected
                            ? Container(
                                width: 20,
                                decoration: const BoxDecoration(
                                  color: Color(0xFF800000),
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25),
                                    bottomRight: Radius.circular(25),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),
                      buildSizedBoxH(4),
                      CustomImageView(
                        imagePath: navItems[index]['icon'],
                        height: 22,
                        width: 22,
                        color: isSelected
                            ? const Color(0xFF800000)
                            : Colors.black,
                      ),
                      buildSizedBoxH(4),
                      Text(
                        navItems[index]['label'],
                        style: TextStyle(
                          fontSize: 12,
                          color: isSelected
                              ? const Color(0xFF800000)
                              : Colors.black,
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSwapButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.read<HomeBloc>().add(SwapLocationText());
      },
      child: CustomImageView(
        imagePath: Assets.images.svgs.icons.svgIcSwip.path,
      ),
    );
  }
}
