import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_bloc.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_event.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_state.dart';

class OnboardingScreen extends StatelessWidget {
  OnboardingScreen({super.key});

  static Widget builder(BuildContext context) {
    return OnboardingScreen();
  }

  final PageController _controller = PageController();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.light),
    );
    final List<Widget> pages = [
      onBoardingFirstScreen(
        context: context,
        imagePath: Assets.images.pngs.onboarding.pngOnbordingBg1.path,
        titleText: 'Welcome To Z - Ride Anytime!',
        subTitle:
            'Book your cab instantly and enjoy fast, reliable, and safe rides anywhere, anytime with complete ease.',
      ),
      onBoardingFirstScreen(
        context: context,
        imagePath: Assets.images.pngs.onboarding.pngOnbordingBg2.path,
        titleText: 'Enjoy Safe, Fast Comfortable Cab Rides',
        subTitle:
            'Our professional drivers ensure a reliable and smooth travel experience, no matter where you\'re going.',
      ),
      onBoardingFirstScreen(
        context: context,
        imagePath: Assets.images.pngs.onboarding.pngOnbordingBg3.path,
        titleText: 'Book Cabs and Navigate the City With One Tap',
        subTitle: 'Instant cab bookings with live tracking, secure payments, and top - rated drivers always near you.',
      ),
    ];

    return BlocProvider(
      create: (context) => OnboardingBloc(context, _controller),
      child: Scaffold(
        body: Stack(
          children: [
            PageView.builder(
              controller: _controller,
              itemCount: pages.length,
              onPageChanged: (index) {
                context.read<OnboardingBloc>().add(PageChanged(index));
              },
              itemBuilder: (_, index) => pages[index],
            ),
            Positioned(
              bottom: 35,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<OnboardingBloc, OnboardingState>(
                    builder: (context, state) {
                      return SmoothPageIndicator(
                        controller: _controller,
                        count: pages.length,
                        effect: ExpandingDotsEffect(
                          dotHeight: 10,
                          dotWidth: 18,
                          activeDotColor: Theme.of(context).customColors.primaryColor!,
                          dotColor: Theme.of(context).customColors.fillColor!,
                        ),
                      );
                    },
                  ),
                  InkWell(
                    onTap: () {
                      context.read<OnboardingBloc>().add(SkipPressed());
                    },
                    child: CustomImageView(height: 38.h, imagePath: Assets.images.pngs.onboarding.pngSkip.path),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget onBoardingFirstScreen({
    required BuildContext context,
    required String imagePath,
    required String titleText,
    required String subTitle,
  }) {
    return BackgroundImage(
      imagePath: imagePath,
      child: Positioned(
        top: 125.h,
        left: 22,
        right: 22,
        child: Column(
          children: [
            Text(
              titleText,
              textAlign: TextAlign.center,
              maxLines: 3,
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: ThemeData().customColors.primaryColor),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0.h),
              child: Text(
                subTitle,
                textAlign: TextAlign.center,
                maxLines: 3,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: ThemeData().customColors.textdarkcolor,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            buildSizedBoxH(12),
            CustomImageView(height: 50.h, imagePath: Assets.images.pngs.onboarding.pngIconOnbordingArrow.path),
          ],
        ),
      ),
    );
  }
}
