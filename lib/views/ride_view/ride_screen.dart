import 'package:zride/widgets/common_widget/common_ride_option.dart';

import '../../core/utils/app_exports.dart';
import '../../widgets/common_widget/common_location_textfield.dart';

// ignore: must_be_immutable
class RideScreen extends StatefulWidget {
  const RideScreen({super.key});

  static Widget builder(BuildContext context) {
    return RideScreen();
  }

  @override
  State<RideScreen> createState() => _RideScreenState();
}

class _RideScreenState extends State<RideScreen> {
  String? selectedId;

  final List<RideOptionModel> dummyRideOptions = [
    RideOptionModel(
      id: 'car',
      name: 'GoCab b Medium',
      estimatedTime: 8,
      capacity: 4,
      price: 23.0,
    ),
    RideOptionModel(
      id: 'suv',
      name: 'GoCab Protect +',
      estimatedTime: 10,
      capacity: 6,
      price: 30.0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: CustomImageView(
              imagePath: Assets.images.pngs.login.pngPermissionBg.path,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20.0, right: 20.0, top: 88.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        LocationTextField(
                          value: '', //state.startLocation,
                          hint: 'Start Location',
                          onChanged: (value) {},
                        ),
                        _buildSwapButton(context),
                        LocationTextField(
                          value: '', // state.endLocation,
                          hint: 'Your Location',
                          onChanged: (value) {},
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.58,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.arrow_back_ios, color: Colors.black),
                          buildSizedboxW(8),
                          Text(
                            'Available Options',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                      buildSizedBoxH(28.h),
                      RideOptionSelector(
                        rideOptions: dummyRideOptions,
                        selectedRideOptionId: selectedId,
                        onOptionSelected: (id) {
                          setState(() {
                            selectedId = id;
                          });
                        },
                      ),
                      
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwapButton(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: CustomImageView(
        imagePath: Assets.images.svgs.icons.svgIcSwip.path,
      ),
    );
  }
}
