import 'package:flutter/gestures.dart';
import 'package:pinput/pinput.dart';
import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/otp_bloc/otp_bloc.dart';
import 'package:zride/viewmodels/otp_bloc/otp_event.dart';
import 'package:zride/viewmodels/otp_bloc/otp_state.dart';

class OtpScreen extends StatelessWidget {
  final String credential;

  const OtpScreen({super.key, required this.credential});

  static Widget builder(BuildContext context) {
    return const OtpScreen(credential: '');
  }

  @override
  Widget build(BuildContext context) {
    final pinController = TextEditingController();

    return BlocConsumer<OtpBloc, OtpState>(
      listener: (context, state) {
        if (state.isSuccess) {
          // Navigator.pushNamed(context, '/permissions');
        } else if (state.errorMessage != null) {
          // Fluttertoast.showToast(msg: state.errorMessage!);
        }
      },
      builder: (context, state) {
        final bloc = context.read<OtpBloc>();

        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
          ),
        );

        final defaultPinTheme = PinTheme(
          width: 56,
          height: 56,
          textStyle: const TextStyle(fontSize: 20, color: Colors.black),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey),
          ),
        );

        final focusedPinTheme = defaultPinTheme.copyWith(
          decoration: BoxDecoration(
            color: const Color(0xFFFFF5F5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.3),
                blurRadius: 2,
                spreadRadius: 0.9,
              ),
            ],
          ),
        );

        final submittedPinTheme = defaultPinTheme.copyWith(
          decoration: BoxDecoration(
            color: const Color(0xFFFFF5F5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.3),
                blurRadius: 1,
                spreadRadius: 0.9,
                blurStyle: BlurStyle.inner,
              ),
            ],
          ),
        );

        return Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              Positioned(
                child: CustomImageView(
                  imagePath: Assets.images.pngs.login.pngLoginBg.path,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 20.0, right: 20.0, top: 62.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        NavigatorService.popAndPushNamed(AppRoutes.loginScreen);
                      },
                      child: CustomImageView(
                        height: 40,
                        imagePath: Assets.images.svgs.icons.svgIcBackArrow.path,
                      ),
                    ),
                    buildSizedBoxH(44),
                    Text(
                      "OTP Verification Code",
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                    ),
                    buildSizedBoxH(16),
                    RichText(
                      text: TextSpan(
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                        children: [
                          TextSpan(
                            text:
                                'We have sent the verification code to your credential ',
                          ),
                          TextSpan(
                            text: '+91 (9999) 999 999', //credential,
                            style: Theme.of(context).textTheme.labelLarge
                                ?.copyWith(
                                  color: ThemeData().customColors.primaryColor,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.69,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,

                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(30),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Text(
                            "Enter the 6 digits sent to your credential linked with the account.",
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.labelLarge
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.greyborder,
                                ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 24.h),
                            child: Pinput(
                              length: 6,
                              controller: pinController,
                              defaultPinTheme: defaultPinTheme,
                              focusedPinTheme: focusedPinTheme,
                              submittedPinTheme: submittedPinTheme,
                              showCursor: true,
                              onCompleted: (pin) {
                                print('Entered OTP: $pin');
                                // bloc.add(
                                //   SubmitOtp(credential: credential, otp: pin),
                                // );
                              },
                            ),
                          ),
                          RichText(
                            text: TextSpan(
                              style: Theme.of(context).textTheme.bodyMedium,
                              children: [
                                TextSpan(
                                  text: 'Code expire in ',
                                  style: Theme.of(context).textTheme.labelLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.secondary,
                                      ),
                                ),
                                TextSpan(
                                  text:
                                      '${_formatTime(state.secondsRemaining)}s',
                                  style: Theme.of(context).textTheme.labelLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(
                                          context,
                                        ).customColors.hinttextcolor,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          buildSizedBoxH(10.h),
                          RichText(
                            text: TextSpan(
                              style: Theme.of(context).textTheme.labelLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.secondary,
                                  ),
                              children: [
                                TextSpan(
                                  text: "Didn't get code? ",
                                  style: Theme.of(context).textTheme.labelLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.secondary,
                                      ),
                                ),
                                TextSpan(
                                  text: 'Resend code',
                                  style: Theme.of(context).textTheme.labelLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                      ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = state.secondsRemaining == 0
                                        ? () {
                                            bloc.add(ResendOtpPressed());
                                          }
                                        : null,
                                ),
                              ],
                            ),
                          ),
                          buildSizedBoxH(45),
                          CustomElevatedButton(
                            text: state.isSubmitting
                                ? "Verifying..."
                                : "Confirm",
                            onPressed: () {
                              NavigatorService.pushAndRemoveUntil(
                                AppRoutes.permissionScreen,
                              );
                            },
                            //  state.isSubmitting
                            //     ? null
                            //     : () {
                            //         if (pinController.text.length == 6) {
                            //           bloc.add(
                            //             SubmitOtp(
                            //               credential: credential,
                            //               otp: pinController.text,
                            //             ),
                            //           );
                            //         }
                            //       },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTime(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }
}
