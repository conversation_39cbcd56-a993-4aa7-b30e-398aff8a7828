import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:zride/viewmodels/profile_bloc/profile_event.dart';
import 'package:zride/viewmodels/profile_bloc/profile_state.dart';
import 'package:zride/widgets/common_widget/profile_textfield.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  static Widget builder(BuildContext context) {
    return const ProfileScreen();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProfileBloc>().add(LoadProfile());
    });
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Positioned(
            child: CustomImageView(
              imagePath: Assets.images.pngs.login.pngLoginBg.path,
            ),
          ),

          SizedBox(
            height: MediaQuery.of(context).size.height * 0.22,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Center(
                  child: Text(
                    "Profile",
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),

                Align(
                  alignment: Alignment.centerLeft,
                  child: GestureDetector(
                    onTap: () {
                      NavigatorService.popAndPushNamed(AppRoutes.homeScreen);
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: CustomImageView(
                        height: 40,
                        imagePath: Assets.images.svgs.icons.svgIcBackArrow.path,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.775,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    buildSizedBoxH(50),
                    Text('Jenny Wilson', style: TextStyle(fontSize: 20)),
                    buildSizedBoxH(30),
                    BlocBuilder<ProfileBloc, ProfileState>(
                      builder: (context, state) {
                        return Column(
                          children: [
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcContact.path,
                              label: 'Name',
                              iconColor: Colors.white,
                              ovalColor1: Colors.white.withOpacity(0.12),
                              backgroundColor: Colors.black,
                              ovalColor: Theme.of(context).colorScheme.primary,
                              value: '',
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdatePhoneNumber(val)),
                            ),
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcCall.path,
                              label: 'Phone Number',
                              value: state.phoneNumber,
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdatePhoneNumber(val)),
                            ),
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcLocation.path,
                              label: 'Location',
                              value: state.location,
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdateLocation(val)),
                            ),
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcEmail.path,
                              label: 'Email',
                              value: state.email,
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdateEmail(val)),
                            ),
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcGender.path,
                              label: 'Gender',
                              value: state.gender,
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdateGender(val)),
                            ),
                            ProfileFieldTextField(
                              icon: Assets.images.svgs.icons.svgIcCalander.path,
                              label: 'Date of Birth',
                              value: state.dob,
                              onChanged: (val) => context
                                  .read<ProfileBloc>()
                                  .add(UpdateDOB(val)),
                            ),
                          ],
                        );
                      },
                    ),

                    buildSizedBoxH(45),
                    CustomElevatedButton(
                      text: "Confirm",
                      onPressed: () {
                        context.read<ProfileBloc>().add(SubmitProfile());
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.16,
            right: MediaQuery.of(context).size.width * 0.38,
            left: MediaQuery.of(context).size.width * 0.31,
            child: Stack(
              alignment: Alignment.bottomRight,
              children: [
                ClipOval(
                  child: CustomImageView(
                    height: 110,
                    imagePath: Assets.images.pngs.other.pngProfile.path,
                    radius: BorderRadius.circular(100),
                  ),
                ),
                Positioned(
                  bottom: -10,
                  right: -8,
                  child: CustomImageView(
                    imagePath: Assets.images.svgs.icons.svgIcCamera.path,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
