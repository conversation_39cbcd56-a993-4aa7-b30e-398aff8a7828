import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/splash_bloc/splash_bloc.dart';
import 'package:zride/viewmodels/splash_bloc/splash_event.dart';
import 'package:zride/viewmodels/splash_bloc/splash_state.dart';

class SplashScreen extends StatelessWidget {
  static Widget builder(BuildContext context) {
    return const SplashScreen();
  }

  const SplashScreen({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.light),
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SplashBloc>().add(StartSplashTimer(context));
    });
    return BlocBuilder<SplashBloc, SplashState>(
      builder: (context, state) {
        return BackgroundImage(
          imagePath: Assets.images.pngs.authentication.pngSplashBg.path,
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 72.0.w),
              child: CustomImageView(imagePath: Assets.images.pngs.authentication.pngZrideLogo.path),
            ),
          ),
        );
      },
    );
  }
}
