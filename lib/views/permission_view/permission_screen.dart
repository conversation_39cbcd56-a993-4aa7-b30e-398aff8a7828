import '../../core/utils/app_exports.dart';

class PermissionScreen extends StatelessWidget {
  const PermissionScreen({super.key});

  static Widget builder(BuildContext context) {
    return const PermissionScreen();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Positioned(
            child: CustomImageView(
              imagePath: Assets.images.pngs.login.pngPermissionBg.path,
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.67,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      CustomElevatedButton(
                        text: "Allow Permission",
                        onPressed: () {},
                      ),
                      buildSizedBoxH(22.h),
                      CustomElevatedButton(
                        backgroundColor: Theme.of(
                          context,
                        ).customColors.fillColor,
                        borderColor: Theme.of(
                          context,
                        ).colorScheme.secondaryContainer,
                        textColor: Theme.of(context).customColors.hinttextcolor,
                        text: "Enter pickup manually",
                        imagePath:
                            Assets.images.svgs.icons.svgIcRightArrowColor.path,
                        onPressed: () {
                          NavigatorService.pushAndRemoveUntil(
                            AppRoutes.homeScreen,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
