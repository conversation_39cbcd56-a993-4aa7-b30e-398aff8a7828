// class SalesAppAuthIntilizeModel {
//   String? deviceId;
//   String? callerIdentifier;

//   SalesAppAuthIntilizeModel({this.deviceId, this.callerIdentifier});

//   SalesAppAuthIntilizeModel.fromJson(Map<String, dynamic> json) {
//     deviceId = json['deviceId'];
//     callerIdentifier = json['callerIdentifier'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['deviceId'] = deviceId;
//     data['callerIdentifier'] = callerIdentifier;
//     return data;
//   }
// }
