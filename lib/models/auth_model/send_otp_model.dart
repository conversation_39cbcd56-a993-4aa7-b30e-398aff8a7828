// class SendOtpModel {
//   int? customerId;
//   String? phoneNumber;
//   int? phoneOTP;

//   SendOtpModel({this.customerId, this.phoneNumber, this.phoneOTP});

//   SendOtpModel.fromJson(Map<String, dynamic> json) {
//     customerId = json['customerId'];
//     phoneNumber = json['phoneNumber'];
//     phoneOTP = json['phoneOTP'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['customerId'] = customerId;
//     data['phoneNumber'] = phoneNumber;
//     data['phoneOTP'] = phoneOTP;
//     return data;
//   }
// }
