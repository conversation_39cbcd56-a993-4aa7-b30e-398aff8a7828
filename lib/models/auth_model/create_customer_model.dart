// class CreateCustomerModel {
//   int? customerId;
//   String? customerType;
//   String? firstName;
//   String? lastName;
//   String? phone;
//   int? defaultAddressId;
//   int? draftOrderId;

//   CreateCustomerModel({
//     this.customerId,
//     this.customerType,
//     this.firstName,
//     this.lastName,
//     this.phone,
//     this.defaultAddressId,
//     this.draftOrderId,
//   });

//   CreateCustomerModel.fromJson(Map<String, dynamic> json) {
//     customerId = json['customerId'];
//     customerType = json['customerType'];
//     firstName = json['firstName'];
//     lastName = json['lastName'];
//     phone = json['phone'];
//     defaultAddressId = json['defaultAddressId'];
//     draftOrderId = json['draftOrderId'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['customerId'] = customerId;
//     data['customerType'] = customerType;
//     data['firstName'] = firstName;
//     data['lastName'] = lastName;
//     data['phone'] = phone;
//     data['defaultAddressId'] = defaultAddressId;
//     data['draftOrderId'] = draftOrderId;
//     return data;
//   }
// }
