import 'package:dio/dio.dart';

class AuthRepository {
  final Dio dio = Dio(BaseOptions(baseUrl: 'https://your-api.com'));

  Future<void> resendOtp() async {
    await dio.post('/resend-otp', data: {'credential': ''});
  }

  Future<void> verifyOtp(String credential, String otp) async {
    final response = await dio.post(
      '/verify-otp',
      data: {'credential': credential, 'otp': otp},
    );

    if (response.statusCode != 200) {
      throw Exception("OTP verification failed");
    }
  }
}
