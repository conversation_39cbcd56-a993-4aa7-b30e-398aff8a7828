class OtpState {
  final int secondsRemaining;
  final bool isSubmitting;
  final bool isSuccess;
  final String? errorMessage;
  final bool isTimerStarted;

  OtpState({
    required this.secondsRemaining,
    this.isSubmitting = false,
    this.isSuccess = false,
    this.errorMessage,
    this.isTimerStarted = false,
  });

  factory OtpState.initial() => OtpState(secondsRemaining: 180);

  OtpState copyWith({
    int? secondsRemaining,
    bool? isSubmitting,
    bool? isSuccess,
    String? errorMessage,
    bool? isTimerStarted,
  }) {
    return OtpState(
      secondsRemaining: secondsRemaining ?? this.secondsRemaining,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage,
      isTimerStarted: isTimerStarted ?? this.isTimerStarted,
    );
  }
}
