import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zride/repository/auth_repository.dart';
import 'package:zride/viewmodels/otp_bloc/otp_event.dart';
import 'package:zride/viewmodels/otp_bloc/otp_state.dart';

class OtpBloc extends Bloc<OtpEvent, OtpState> {
  Timer? _timer;
  final AuthRepository authRepository;

  OtpBloc(this.authRepository) : super(OtpState.initial()) {
    on<StartTimer>(_onStartTimer);
    on<ResendOtpPressed>(_onResendOtp);
    on<SubmitOtp>(_onSubmitOtp);

    // Automatically start timer when bloc is created
    add(StartTimer());
  }

  Future<void> _onStartTimer(StartTimer event, Emitter<OtpState> emit) async {
    _timer?.cancel(); // Cancel previous timer if any

    int seconds = 180;
    emit(state.copyWith(secondsRemaining: seconds, isTimerStarted: true));

    while (seconds > 0) {
      await Future.delayed(const Duration(seconds: 1));

      seconds--;

      if (emit.isDone) return; // Prevent calling emit after bloc is closed
      emit(state.copyWith(secondsRemaining: seconds));
    }

    // Final emit to ensure 0 is shown
    if (!emit.isDone) {
      emit(state.copyWith(secondsRemaining: 0));
    }
  }

  Future<void> _onResendOtp(
    ResendOtpPressed event,
    Emitter<OtpState> emit,
  ) async {
    try {
      // Set loading state
      emit(state.copyWith(isSubmitting: true, errorMessage: null));

      // Call resend API - uncomment when ready
      // await authRepository.resendOtp();

      // Reset loading state
      emit(state.copyWith(isSubmitting: false));

      // Restart timer after successful resend
      add(StartTimer());
    } catch (e) {
      emit(
        state.copyWith(
          isSubmitting: false,
          errorMessage: 'Failed to resend OTP. Please try again.',
        ),
      );
    }
  }

  Future<void> _onSubmitOtp(SubmitOtp event, Emitter<OtpState> emit) async {
    emit(state.copyWith(isSubmitting: true, errorMessage: null));

    try {
      // Uncomment when API is ready
      // final success = await authRepository.verifyOtp(
      //   credential: event.credential,
      //   otp: event.otp,
      // );

      // Simulate API call for now
      await Future.delayed(const Duration(seconds: 2));

      // Mock success - replace with actual API response
      bool success = event.otp == "123456"; // For testing

      if (success) {
        // Cancel timer on successful verification
        _timer?.cancel();
        emit(state.copyWith(isSuccess: true, isSubmitting: false));
      } else {
        emit(
          state.copyWith(
            isSubmitting: false,
            errorMessage: 'Invalid OTP. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isSubmitting: false,
          errorMessage: 'Something went wrong. Please try again.',
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
