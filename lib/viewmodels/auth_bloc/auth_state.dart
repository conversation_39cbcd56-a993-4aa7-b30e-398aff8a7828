import 'package:equatable/equatable.dart';

abstract class AuthState extends Equatable {
  const AuthState();
  @override
  List<Object?> get props => [];
}

class <PERSON>ginInitial extends AuthState {}

class LoginLoading extends AuthState {}

class RegisterSuccess extends AuthState {}

class OtpSentSuccess extends AuthState {
  final String email;

  const OtpSentSuccess(this.email);

  @override
  List<Object?> get props => [email];
}

class LoginError extends AuthState {
  final String message;

  const LoginError(this.message);

  @override
  List<Object?> get props => [message];
}

class RegisterToggleChanged extends AuthState {
  final bool isRegister;
  const RegisterToggleChanged(this.isRegister);

  @override
  List<Object?> get props => [isRegister];
}
