import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();
  @override
  List<Object?> get props => [];
}

class RegisterUserEvent extends AuthEvent {
  final String name;
  final String address;
  final String mobile;
  final String password;

  const RegisterUserEvent({
    required this.name,
    required this.address,
    required this.mobile,
    required this.password,
  });

  @override
  List<Object?> get props => [name, address, mobile, password];
}

class SendOtpEvent extends AuthEvent {
  final String email;

  const SendOtpEvent({required this.email});

  @override
  List<Object?> get props => [email];
}

class ToggleRegisterLoginEvent extends AuthEvent {
  final bool isRegister;
  const ToggleRegisterLoginEvent(this.isRegister);

  @override
  List<Object?> get props => [isRegister];
}
