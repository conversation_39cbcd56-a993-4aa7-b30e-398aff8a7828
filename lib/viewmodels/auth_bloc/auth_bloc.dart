import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/auth_bloc/auth_event.dart';
import 'package:zride/viewmodels/auth_bloc/auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(LoginInitial()) {
    on<RegisterUserEvent>(_onRegisterUser);
    on<SendOtpEvent>(_onSendOtp);
    on<ToggleRegisterLoginEvent>(_onToggleRegisterLogin);
  }

  Future<void> _onRegisterUser(
    RegisterUserEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(LoginLoading());

    try {
      // Replace this with your API call
      await Future.delayed(Duration(seconds: 2));

      //  Make actual API call to register user
      // final response = await apiService.register(...);
      emit(RegisterSuccess());
    } catch (e) {
      emit(LoginError("Registration failed: ${e.toString()}"));
    }
  }

  Future<void> _onSendOtp(SendOtpEvent event, Emitter<AuthState> emit) async {
    emit(LoginLoading());

    try {
      // Replace this with your API call
      NavigatorService.pushNamed(AppRoutes.otpScreen);
      await Future.delayed(Duration(seconds: 2));

      //  Send OTP API call
      emit(OtpSentSuccess(event.email));
    } catch (e) {
      emit(LoginError("OTP send failed: ${e.toString()}"));
    }
  }

  Future<void> _onToggleRegisterLogin(
    ToggleRegisterLoginEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(RegisterToggleChanged(event.isRegister));
  }
}
