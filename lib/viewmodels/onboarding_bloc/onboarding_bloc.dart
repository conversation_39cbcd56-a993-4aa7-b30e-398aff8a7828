import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_event.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_state.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  final PageController pageController;
  final BuildContext context;

  OnboardingBloc(this.context, this.pageController)
    : super(OnboardingState(currentPage: 0)) {
    on<PageChanged>((event, emit) {
      emit(state.copyWith(currentPage: event.page));
    });

    on<SkipPressed>((event, emit) {
      print("onbording is skippress 1");
      if (state.currentPage < 2) {
        print("onbording is skippress 2");
        pageController.animateToPage(
          state.currentPage + 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );

        print("onbording is skippress 7");
        emit(state.copyWith(currentPage: state.currentPage + 1));
      } else {
        print("onbording is skippress 8");
        Prefobj.preferences?.put(Prefkeys.ONBOARDING, true);
        NavigatorService.pushAndRemoveUntil(AppRoutes.loginScreen);
      }
    });
  }
}
