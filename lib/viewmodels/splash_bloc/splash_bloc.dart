import 'package:zride/core/utils/app_exports.dart';
import 'splash_event.dart';
import 'splash_state.dart';

class SplashBloc extends Bloc<SplashEvent, SplashState> {
  SplashBloc() : super(SplashInitial()) {
    on<StartSplashTimer>(_onStartSplashTimer);
  }

  Future<void> _onStartSplashTimer(StartSplashTimer event, Emitter<SplashState> emit) async {
    await Future.delayed(const Duration(seconds: 3));

    final authToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN);
    final onboardingDone = Prefobj.preferences?.get(Prefkeys.ONBOARDING) == true;

    if (authToken == null) {
      Navigator.pushReplacementNamed(
        event.context,
        onboardingDone ? AppRoutes.loginScreen : AppRoutes.onboardingScreen,
      );
    } else {
      Navigator.pushReplacementNamed(event.context, AppRoutes.loginScreen);
    }
  }

  Future<void> clearCache() async {
    await DefaultCacheManager().emptyCache();
  }
}
