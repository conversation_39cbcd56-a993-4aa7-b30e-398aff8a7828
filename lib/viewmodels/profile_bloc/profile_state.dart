class ProfileState {
  final String phoneNumber;
  final String location;
  final String email;
  final String gender;
  final String dob;

  ProfileState({
    required this.phoneNumber,
    required this.location,
    required this.email,
    required this.gender,
    required this.dob,
  });

  ProfileState copyWith({
    String? phoneNumber,
    String? location,
    String? email,
    String? gender,
    String? dob,
  }) {
    return ProfileState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      location: location ?? this.location,
      email: email ?? this.email,
      gender: gender ?? this.gender,
      dob: dob ?? this.dob,
    );
  }
}
