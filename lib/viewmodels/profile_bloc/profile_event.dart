abstract class ProfileEvent {}

class LoadProfile extends ProfileEvent {}

class SubmitProfile extends ProfileEvent {}

class UpdatePhoneNumber extends ProfileEvent {
  final String phoneNumber;
  UpdatePhoneNumber(this.phoneNumber);
}

class UpdateLocation extends ProfileEvent {
  final String location;
  UpdateLocation(this.location);
}

class UpdateEmail extends ProfileEvent {
  final String email;
  UpdateEmail(this.email);
}

class UpdateGender extends ProfileEvent {
  final String gender;
  UpdateGender(this.gender);
}

class UpdateDOB extends ProfileEvent {
  final String dob;
  UpdateDOB(this.dob);
}
