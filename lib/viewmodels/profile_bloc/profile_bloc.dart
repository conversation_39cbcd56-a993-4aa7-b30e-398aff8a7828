import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/viewmodels/profile_bloc/profile_event.dart';
import 'package:zride/viewmodels/profile_bloc/profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc()
    : super(
        ProfileState(
          phoneNumber: '',
          location: '',
          email: '',
          gender: '',
          dob: '',
        ),
      ) {
    on<LoadProfile>((event, emit) async {
      // fetch from API
      final fetched = await fetchUserProfile();
      emit(fetched);
    });

    on<UpdatePhoneNumber>((event, emit) {
      emit(state.copyWith(phoneNumber: event.phoneNumber));
    });

    on<UpdateLocation>((event, emit) {
      emit(state.copyWith(location: event.location));
    });

    on<UpdateEmail>((event, emit) {
      emit(state.copyWith(email: event.email));
    });

    on<UpdateGender>((event, emit) {
      emit(state.copyWith(gender: event.gender));
    });

    on<UpdateDOB>((event, emit) {
      emit(state.copyWith(dob: event.dob));
    });
    on<SubmitProfile>((event, emit) async {
      try {
        // Call the update profile API
        await updateUserProfile(state);
        // You can show a success message/snackbar here if needed
      } catch (e) {
        // Handle error (show a snackbar, log error, etc.)
      }
    });
  }

  Future<ProfileState> fetchUserProfile() async {
    // mock API response
    return ProfileState(
      phoneNumber: '9876543210',
      location: 'Surat',
      email: '<EMAIL>',
      gender: 'Male',
      dob: '1995-08-25',
    );
  }

  Future<void> updateUserProfile(ProfileState state) async {
    // This should be replaced with your real API logic
    print("Updating profile with:");
    print("Phone: ${state.phoneNumber}");
    print("Location: ${state.location}");
    print("Email: ${state.email}");
    print("Gender: ${state.gender}");
    print("DOB: ${state.dob}");

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    // Example API logic:
    /*
  final response = await http.post(
    Uri.parse("https://your.api/update-profile"),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      "phoneNumber": state.phoneNumber,
      "location": state.location,
      "email": state.email,
      "gender": state.gender,
      "dob": state.dob,
    }),
  );
  if (response.statusCode != 200) {
    throw Exception("Failed to update profile");
  }
  */
  }
}
