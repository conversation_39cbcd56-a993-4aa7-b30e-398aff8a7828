class HomeState {
  final int selectedBottomNavIndex;
  final String startLocation;
  final String endLocation;
  final String searchQuery;

  const HomeState({
    this.selectedBottomNavIndex = 0,
    this.startLocation = '',
    this.endLocation = '',
    this.searchQuery = '',
  });

  HomeState copyWith({
    int? selectedBottomNavIndex,
    String? startLocation,
    String? endLocation,
    String? searchQuery,
  }) {
    return HomeState(
      selectedBottomNavIndex: selectedBottomNavIndex ?? this.selectedBottomNavIndex,
      startLocation: startLocation ?? this.startLocation,
      endLocation: endLocation ?? this.endLocation,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}