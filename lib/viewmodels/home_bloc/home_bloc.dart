import 'package:zride/core/utils/app_exports.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc() : super(const HomeState()) {
    on<ChangeBottomNavIndex>(_onChangeBottomNavIndex);
    on<SwapLocationText>(_onSwapLocationText);
    on<UpdateStartLocation>(_onUpdateStartLocation);
    on<UpdateEndLocation>(_onUpdateEndLocation);
    on<UpdateSearchQuery>(_onUpdateSearchQuery);
  }

  void _onChangeBottomNavIndex(
    ChangeBottomNavIndex event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(selectedBottomNavIndex: event.index));

    // Navigate to profile screen if settings tab is selected
    if (event.index == 3) {
      NavigatorService.pushNamed(AppRoutes.profileScreen);
    } else if (event.index == 2) {
      NavigatorService.pushNamed(AppRoutes.rideScreen);
    }
  }

  void _onSwapLocationText(SwapLocationText event, Emitter<HomeState> emit) {
    emit(
      state.copyWith(
        startLocation: state.endLocation,
        endLocation: state.startLocation,
      ),
    );
  }

  void _onUpdateStartLocation(
    UpdateStartLocation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(startLocation: event.location));
  }

  void _onUpdateEndLocation(UpdateEndLocation event, Emitter<HomeState> emit) {
    emit(state.copyWith(endLocation: event.location));
  }

  void _onUpdateSearchQuery(UpdateSearchQuery event, Emitter<HomeState> emit) {
    emit(state.copyWith(searchQuery: event.query));
  }
}
