abstract class HomeEvent {}

class ChangeBottomNavIndex extends HomeEvent {
  final int index;
  ChangeBottomNavIndex(this.index);
}

class SwapLocationText extends HomeEvent {}

class UpdateStartLocation extends HomeEvent {
  final String location;
  UpdateStartLocation(this.location);
}

class UpdateEndLocation extends HomeEvent {
  final String location;
  UpdateEndLocation(this.location);
}

class UpdateSearchQuery extends HomeEvent {
  final String query;
  UpdateSearchQuery(this.query);
}