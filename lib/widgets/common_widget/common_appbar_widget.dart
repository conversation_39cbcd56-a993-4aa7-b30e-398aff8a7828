import 'package:flutter/material.dart';
import 'package:zride/core/utils/app_exports.dart';

class ZRideAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final Color? shadowColor;
  final Color? iconColor;
  final double elevation;
  final bool? centerTitle;
  final double? toolbarHeight;

  const ZRideAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.shadowColor,
    this.iconColor,
    this.elevation = 6.0,
    this.centerTitle = false,
    this.toolbarHeight,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Theme.of(context).customColors.fillColor,
      automaticallyImplyLeading: false,
      centerTitle: centerTitle,
      leading:
          showBackButton
              ? CustomImageView(
                imagePath: 'Assets.images.svgs.icons.svgBackArrow.path',
                margin: EdgeInsets.all(15),
                onTap: onBackPressed ?? () => NavigatorService.goBack(),
              )
              : null,
      elevation: elevation,
      shadowColor:
          shadowColor?.withValues(alpha: 0.18) ?? Theme.of(context).customColors.dividerColor?.withValues(alpha: 0.12),
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(bottom: Radius.circular(24.0))),
      titleSpacing: 0.0,
      title:
          titleWidget ??
          (title != null
              ? Text(
                title!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 17.0.sp, fontWeight: FontWeight.bold),
              )
              : null),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);
}
