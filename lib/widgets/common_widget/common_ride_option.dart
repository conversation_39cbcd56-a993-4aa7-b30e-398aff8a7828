import 'package:flutter/material.dart';
import 'package:zride/core/utils/app_exports.dart';

class RideOptionModel {
  final String id;
  final String name;
  final int estimatedTime;
  final int capacity;
  final double price;

  RideOptionModel({
    required this.id,
    required this.name,
    required this.estimatedTime,
    required this.capacity,
    required this.price,
  });
}

class RideOptionSelector extends StatelessWidget {
  final List<RideOptionModel> rideOptions;
  final String? selectedRideOptionId;
  final ValueChanged<String> onOptionSelected;

  const RideOptionSelector({
    super.key,
    required this.rideOptions,
    required this.selectedRideOptionId,
    required this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: rideOptions.map((option) {
        final isSelected = option.id == selectedRideOptionId;
        return GestureDetector(
          onTap: () => onOptionSelected(option.id),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
              color: isSelected
                  ? null
                  : Theme.of(context).colorScheme.primary.withOpacity(0.1),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
                BoxShadow(
                  color: Colors.white,
                  blurRadius: 1,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: Assets.images.pngs.other.pngCar.path,
                ),

                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        option.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      VerticalDivider(),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          commonTag(
                            context,
                            Assets.images.svgs.icons.svgIcTimer.path,
                            '${option.estimatedTime} min',
                          ),
                          commonTag(
                            context,
                            Assets.images.svgs.icons.svgIcPeople.path,
                            '${option.capacity}',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$${option.price.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget commonTag(BuildContext context, String imagePath, text) {
    return Row(
      children: [
        CustomImageView(imagePath: imagePath),
        buildSizedboxW(4),
        Text(
          text,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: Theme.of(context).colorScheme.secondary,
          ),
        ),
      ],
    );
  }
}
