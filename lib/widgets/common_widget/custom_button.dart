import 'package:flutter/cupertino.dart';
import 'package:zride/core/utils/app_exports.dart';

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    this.decoration,
    this.margin,
    this.onPressed,
    this.buttonStyle,
    this.alignment,
    this.buttonTextStyle,
    this.isDisabled = false,
    this.height,
    this.width,
    this.iconSpacing,
    this.isLoading = false,
    this.secondary = false,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
    this.imagePath,
    required this.text,
  });

  final BoxDecoration? decoration;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onPressed;
  final ButtonStyle? buttonStyle;
  final Alignment? alignment;
  final TextStyle? buttonTextStyle;
  final bool isDisabled;
  final double? height;
  final double? width;
  final double? iconSpacing;
  final bool isLoading;
  final String text;
  final String? imagePath;
  final bool secondary;
  final double? borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 54.h,
      width: width ?? MediaQuery.of(context).size.width,
      margin: margin,
      decoration:
          decoration ??
          BoxDecoration(
            color: backgroundColor ?? Theme.of(context).colorScheme.primary,
            border: Border.all(color: borderColor ?? Colors.transparent),
            borderRadius: BorderRadius.circular(borderRadius ?? 100),
          ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 100),
          ),
          padding: EdgeInsets.zero,
          textStyle: Theme.of(context).textTheme.labelLarge,
        ),
        onPressed: isDisabled
            ? null
            : () {
                HapticFeedback.lightImpact();
                if (onPressed != null) onPressed!();
              },
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (isLoading)
              const CupertinoActivityIndicator()
            else
              Center(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: secondary
                      ? Theme.of(context).textTheme.labelLarge
                      : buttonTextStyle ??
                            Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: isDisabled
                                  ? Theme.of(context).disabledColor
                                  : textColor ??
                                        Theme.of(context).colorScheme.onPrimary,
                            ),
                ),
              ),

            Positioned(
              right: iconSpacing ?? 8.w,
              child: Align(
                alignment: Alignment.centerRight,
                child: CustomImageView(
                  height: 42.h,
                  imagePath:
                      imagePath ??
                      Assets.images.svgs.icons.svgIcRightArrow.path,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
