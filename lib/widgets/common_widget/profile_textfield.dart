import 'package:zride/core/utils/app_exports.dart';

class ProfileFieldTextField extends StatelessWidget {
  final String icon;
  final String label;
  final String value;
  final Color? backgroundColor;
  final Color? ovalColor;
  final Color? ovalColor1;
  final Color? iconColor;
  final ValueChanged<String> onChanged;

  const ProfileFieldTextField({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.backgroundColor,
    this.ovalColor,
    this.ovalColor1,
    this.iconColor,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(text: value);
    controller.selection = TextSelection.fromPosition(
      TextPosition(offset: controller.text.length),
    );

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.symmetric(horizontal: 8),

      decoration: BoxDecoration(
        color: backgroundColor ?? Color(0xFF982221).withOpacity(0.1),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF333333).withOpacity(0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
      ),

      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: ovalColor1 ?? Colors.white,
            child: CustomImageView(
              imagePath: icon,
              color: iconColor ?? Colors.black54,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: iconColor ?? Colors.black,
              ),
              decoration: InputDecoration(
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                border: InputBorder.none,
                hintText: value.isEmpty ? label : null,
                hintStyle: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: iconColor ?? Colors.black,
                ),
              ),
            ),
          ),
          CircleAvatar(
            backgroundColor: ovalColor ?? Colors.white,
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: iconColor ?? Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }
}
