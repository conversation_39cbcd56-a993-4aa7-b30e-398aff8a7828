import 'package:zride/core/utils/app_exports.dart';

class LocationTextField extends StatelessWidget {
  final String value;
  final String hint;
  final ValueChanged<String> onChanged;

  const LocationTextField({
    super.key,
    required this.value,
    required this.hint,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(16),
      ),
      child: TextField(
        controller: TextEditingController(text: value),
        onChanged: onChanged,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(vertical: 15),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(
              left: 12,
              right: 8,
              top: 10,
              bottom: 10,
            ),
            child: CustomImageView(
              imagePath: Assets.images.svgs.icons.svgIcLocation.path,
              color: const Color(0xFFDB9F32),
            ),
          ),
          hintText: hint,
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSecondaryContainer,
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
