import 'package:zride/core/utils/app_exports.dart';

class CommonTextfields extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String? labelText;
  final String? errorText;
  final String? imagePath;
  final bool enabled;
  final bool obscureText;
  final InputType? type;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final TextInputAction textInputAction;
  final VoidCallback? onTap;
  final IconData? prefixIconData;
  final Color? prefixIconColor;
  final Color? fillColor;
  final Color? borderColor;
  final double borderRadius;
  final EdgeInsetsGeometry? contentPadding;

  const CommonTextfields({
    super.key,
    required this.controller,
    required this.hintText,
    this.labelText,
    this.errorText,
    this.imagePath,
    this.enabled = true,
    this.obscureText = false,
    this.onChanged,
    this.validator,
    this.type,
    this.textInputAction = TextInputAction.next,
    this.onTap,
    this.prefixIconData = Icons.person_outline,
    this.prefixIconColor,
    this.fillColor,
    this.borderColor,
    this.borderRadius = 12.0,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              imagePath != null
                  ? SizedBox(
                      height: 24.h,
                      width: 24.w,
                      child: CustomImageView(imagePath: imagePath),
                    )
                  : SizedBox.shrink(),
              buildSizedboxW(10),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (labelText != null)
                      Text(
                        labelText!,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Theme.of(context).colorScheme.secondary,
                          fontSize: 12.sp,
                        ),
                      ),
                    buildSizedBoxH(6),
                    CustomTextInputField(
                      context: context,
                      type: type ?? InputType.text,
                      hintLabel: hintText,
                      controller: controller,
                      onChanged: onChanged,
                      obscureText: obscureText,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        errorText != null
            ? Text(
                errorText ?? '',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Theme.of(context).colorScheme.primary),
              )
            : SizedBox.shrink(),
      ],
    );
  }
}

class CustomTextInputField extends TextFormField {
  CustomTextInputField({
    super.key,
    required BuildContext context,
    required InputType type,
    required String hintLabel,
    required super.controller,
    super.textInputAction = TextInputAction.next,
    super.maxLines,
    super.minLines,
    super.autovalidateMode = AutovalidateMode.onUnfocus,
    super.validator,
    super.enabled,
    super.readOnly,
    super.expands,
    bool? obscureText,
    super.obscuringCharacter,
    TextInputType? keyboardType,
    Iterable<String>? autoFillHints,
    Widget? suffixIcon,
    Widget? prefixIcon,
    BoxConstraints? boxConstraints,
    List<TextInputFormatter>? inputFormatters,
    EdgeInsetsGeometry? contentPadding,
    Color? fillColor,
    bool? filled,
    TextStyle? hintStyle,
    bool isCapitalized = false,
    String? label,
    bool suffixText = false,

    super.onTap,
    super.onChanged,
    Function(String)? super.onFieldSubmitted,
    super.focusNode,
    super.maxLength,
  }) : assert(
         type != InputType.multiline || textInputAction == TextInputAction.newline,
         'Make textInputAction = TextInputAction.newline',
       ),
       assert(
         (type != InputType.password && type != InputType.newPassword && type != InputType.confirmPassword) ||
             obscureText != null,
         'Make sure your providing obscureText and Wrap Obx on TextInputField',
       ),
       super(
         keyboardType:
             keyboardType ??
             switch (type) {
               InputType.name => TextInputType.name,
               InputType.text => TextInputType.text,
               InputType.email => TextInputType.emailAddress,
               InputType.password => TextInputType.visiblePassword,
               InputType.confirmPassword => TextInputType.visiblePassword,
               InputType.newPassword => TextInputType.visiblePassword,
               InputType.phoneNumber => TextInputType.phone,
               InputType.digits => TextInputType.number,
               InputType.decimalDigits => const TextInputType.numberWithOptions(decimal: true),
               InputType.multiline => TextInputType.multiline,
             },
         autofillHints: [
           if (autoFillHints != null) ...autoFillHints,
           switch (type) {
             InputType.name => AutofillHints.name,
             InputType.email => AutofillHints.email,
             InputType.password => AutofillHints.password,
             InputType.confirmPassword => AutofillHints.password,
             InputType.newPassword => AutofillHints.newPassword,
             InputType.phoneNumber => AutofillHints.telephoneNumber,
             _ => '',
           },
         ],
         textCapitalization: isCapitalized ? TextCapitalization.words : TextCapitalization.none,
         inputFormatters: [
           if (inputFormatters != null) ...inputFormatters,
           if (type == InputType.digits) FilteringTextInputFormatter.digitsOnly,
           if (type == InputType.decimalDigits) FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
         ],
         obscureText: obscureText ?? false,
         textAlignVertical: TextAlignVertical.top,
         style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w500),
         decoration: InputDecoration(
           border: InputBorder.none,
           enabledBorder: InputBorder.none,
           focusedBorder: InputBorder.none,
           errorBorder: InputBorder.none,
           focusedErrorBorder: InputBorder.none,
           floatingLabelBehavior: FloatingLabelBehavior.never,
           hintStyle:
               hintStyle ??
               Theme.of(context).textTheme.bodyMedium!.copyWith(
                 fontSize: 15.sp,
                 color: Theme.of(context).customColors.hinttextcolor,
                 fontWeight: FontWeight.w500,
               ),
           hintText: hintLabel,
           isDense: true,
           contentPadding: contentPadding ?? const EdgeInsets.symmetric(horizontal: 12),
           prefixIconConstraints: boxConstraints,
           fillColor: fillColor ?? Theme.of(context).customColors.fillColor,
           filled: filled ?? true,
           suffixIcon: suffixText
               ? suffixIcon
               : suffixIcon != null
               ? SizedBox(height: 20, width: 20, child: suffixIcon)
               : null,
         ),
       );
}

enum InputType {
  name,
  text,
  email,
  password,
  confirmPassword,
  newPassword,
  phoneNumber,
  digits,
  decimalDigits,
  multiline,
}
