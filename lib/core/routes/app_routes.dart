import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/views/auth_view/login_screen.dart';
import 'package:zride/views/home_view/home_screen.dart';
import 'package:zride/views/onboarding_view/onboarding_screen.dart';
import 'package:zride/views/otp_view/otp_screen.dart';
import 'package:zride/views/permission_view/permission_screen.dart';
import 'package:zride/views/profile_view/profile_screen.dart';
import 'package:zride/views/ride_view/ride_screen.dart';
import 'package:zride/views/splash_view/splash_screen.dart';

class AppRoutes {
  static const String initialRoute = '/';
  static const String onboardingScreen = '/onboardingScreen';
  static const String loginScreen = '/loginScreen';
  static const String otpScreen = '/OtpScreen';
  static const String permissionScreen = '/permissionScreen';
  static const String homeScreen = '/homeScreen';
  static const String profileScreen = '/profileScreen';
  static const String rideScreen = '/rideScreen';

  static Map<String, WidgetBuilder> get routes => {
    initialRoute: SplashScreen.builder,
    onboardingScreen: OnboardingScreen.builder,
    loginScreen: LoginScreen.builder,
    otpScreen: OtpScreen.builder,
    permissionScreen: PermissionScreen.builder,
    homeScreen: HomeScreen.builder,
    profileScreen: ProfileScreen.builder,
    rideScreen: RideScreen.builder,
  };
}
