
import 'package:zride/core/utils/app_exports.dart';

class ImageDimensions {
  final double width;
  final double height;
  final double aspectRatio;

  ImageDimensions({
    required this.width,
    required this.height,
    required this.aspectRatio,
  });
}

Future<ImageDimensions> getImageDimensions(String imageUrl) async {
  final Completer<ImageDimensions> completer = Completer();
  final imageProvider = NetworkImage(imageUrl);
  
  imageProvider.resolve(ImageConfiguration()).addListener(
    ImageStreamListener((ImageInfo info, bool _) {
      final width = info.image.width.toDouble();
      final height = info.image.height.toDouble();
      final aspectRatio = width / height;
      
      completer.complete(ImageDimensions(
        width: width,
        height: height,
        aspectRatio: aspectRatio,
      ));
    })
  );

  return completer.future;
}

double calculateImageHeight(BuildContext context, double aspectRatio) {
  final screenWidth = MediaQuery.of(context).size.width;
  return screenWidth / aspectRatio;
}

void loadImageDimensions(BuildContext context, String imageUrl) {
  final imageProvider = NetworkImage(imageUrl);
    imageProvider.resolve(ImageConfiguration()).addListener(
    ImageStreamListener((info, _) {
      final imageHeight = info.image.height;
      final deviceHeight = imageHeight * 1.0.h;
      // context.read<HomeBloc>().add(UpdateBackgroundImageHeight(deviceHeight));
    })
  );
}