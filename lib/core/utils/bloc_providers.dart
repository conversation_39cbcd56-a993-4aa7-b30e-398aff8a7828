import 'package:zride/core/utils/app_exports.dart';
import 'package:zride/repository/auth_repository.dart';
import 'package:zride/viewmodels/auth_bloc/auth_bloc.dart';
import 'package:zride/viewmodels/home_bloc/home_bloc.dart';
import 'package:zride/viewmodels/onboarding_bloc/onboarding_bloc.dart';
import 'package:zride/viewmodels/otp_bloc/otp_bloc.dart';
import 'package:zride/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:zride/viewmodels/splash_bloc/splash_bloc.dart';

class BlocProviders extends StatelessWidget {
  final Widget child;

  const BlocProviders({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider<AuthRepository>(
      create: (_) => AuthRepository(),
      child: MultiBlocProvider(
        providers: [
          BlocProvider<LocaleBloc>(
            create: (context) =>
                LocaleBloc()..add(SetLocale(locale: const Locale('en'))),
          ),
          BlocProvider<ThemeBloc>(
            create: (context) => ThemeBloc()
              ..add(
                InitializeTheme(isDarkThemeOn: false, followSystemTheme: true),
              ),
          ),
          BlocProvider(
            create: (_) => CheckConnectionCubit()..initializeConnectivity(),
          ),
          BlocProvider<SplashBloc>(create: (context) => SplashBloc()),
          BlocProvider<OnboardingBloc>(
            create: (context) => OnboardingBloc(context, PageController()),
          ),
          BlocProvider<AuthBloc>(create: (context) => AuthBloc()),
          BlocProvider<OtpBloc>(
            create: (context) => OtpBloc(context.read<AuthRepository>()),
          ),
          BlocProvider<ProfileBloc>(create: (_) => ProfileBloc()),
          BlocProvider<HomeBloc>(create: (_) => HomeBloc()),
        ],
        child: child,
      ),
    );
  }
}
