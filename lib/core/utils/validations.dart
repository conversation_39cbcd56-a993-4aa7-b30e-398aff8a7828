
import 'package:zride/core/utils/app_exports.dart';

class AppValidations {
  AppValidations._();

  static String? verificationCodeValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyVerificationCode;
    }
    return null;
  }

  static String? phoneNumberValidation(
    String? value,
    BuildContext context,
    // Country selectedCountry,
  ) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyPhoneNumber;
    }

    // Remove the country code prefix if present
    // String phoneNumber = value.replaceFirst('+${selectedCountry.phoneCode}', '').trim();
    String phoneNumber = '';

    // Ensure only digits are present
    if (!RegExp(r'^[0-9]+$').hasMatch(phoneNumber)) {
      return Lang.of(context).lbl_invalidPhoneNumber;
    }

    // Define min and max length based on country (you may need to use a country data package)
    int minLength = 7; // Default minimum length (adjust per country if needed)
    int maxLength = 15; // Default maximum length (adjust per country if needed)

    if (phoneNumber.length < minLength || phoneNumber.length > maxLength) {
      return Lang.of(context).lbl_invalidPhoneNumber;
    }

    return null;
  }

  static String? nameValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) return Lang.of(context).lbl_emptyName;
    return null;
  }

  static String? passwordValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyPassword;
    }
    return null;
  }

  static String? confirmPasswordValidation(String? value, String otherPasswordValue, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyConfirmPassword;
    }
    if (otherPasswordValue.isEmpty) return null;
    if (otherPasswordValue != value) {
      return Lang.of(context).lbl_passwordMismatch;
    }
    return null;
  }

  static String? emailValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) return Lang.of(context).lbl_emptyEmail;
    if (!isEmail(value)) return Lang.of(context).lbl_invalidEmail;
    return null;
  }

  /// Validates if the field is not empty
  static String? validateRequired(String? value, {String fieldName = 'This field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    return null;
  }

  static String? validateOTP(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter OTP';
    } else if (value.length != 6) {
      return 'OTP must be exactly 6 digits';
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return 'OTP should contain only digits';
    }
    return null;
  }

  static String? validateCVV(String? value) {
    if (value == null || value.isEmpty) {
      return 'CVV is required';
    }

    if (!RegExp(r'^\d{3,4}$').hasMatch(value)) {
      return 'CVV must be 3-4 digits';
    }

    return null;
  }

  static String? validateCardholderName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }

    if (value.trim().length < 3) {
      return 'Enter full name as shown on card';
    }

    return null;
  }

  static String? validateExpiryDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Expiry date is required';
    }

    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
      return 'Use MM/YY format';
    }

    final parts = value.split('/');
    if (parts.length != 2) {
      return 'Invalid format';
    }

    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);

    if (month == null || year == null) {
      return 'Invalid date';
    }

    if (month < 1 || month > 12) {
      return 'Month must be 01-12';
    }
    final currentYear = DateTime.now().year % 100;
    final currentMonth = DateTime.now().month;

    if (year < currentYear || (year == currentYear && month < currentMonth)) {
      return 'Card has expired';
    }

    return null;
  }

  static String? validateCardNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Card number is required';
    }

    return null;
  }
}
