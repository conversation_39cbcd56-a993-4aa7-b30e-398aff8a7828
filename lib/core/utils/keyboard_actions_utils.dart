import 'package:zride/core/utils/app_exports.dart';

KeyboardActionsConfig buildkeyboardConfig(BuildContext context, FocusNode focusNode, VoidCallback ondone) {
  return KeyboardActionsConfig(
    actions: [
      KeyboardActionsItem(
        focusNode: focusNode,
        toolbarButtons: [
          (node) {
            return GestureDetector(
              onTap: () {
                ondone();
                node.unfocus();
              },
              child: Padding(
                padding: EdgeInsets.all(8.0),
                child: Text("Done", style: TextStyle(color: Colors.blue, fontSize: 20.sp, fontWeight: FontWeight.bold)),
              ),
            );
          },
        ],
      ),
    ],
  );
}
