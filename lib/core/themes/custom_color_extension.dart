import 'package:flutter/material.dart';

@immutable
class CustomColors extends ThemeExtension<CustomColors> {
  const CustomColors({
    required this.primaryColor,
    required this.textdarkcolor,
    required this.hinttextcolor,
    required this.fillColor,
    required this.categorytitlecolor,
    required this.categorytbgcolor,
    required this.inactivedotcolor,
    required this.productplaceholdercolor,
    required this.lightgreycolor,
    required this.greencolor,
    required this.greyborder,
    required this.productrdetailbgcolor,
    required this.shadowColor,
    required this.dividerColor,
    required this.darktextcolor,
    required this.orangebordercolor,
    required this.greycontainercolor,
    required this.lightorangeColor,
  });

  final Color? primaryColor;
  final Color? textdarkcolor;
  final Color? hinttextcolor;
  final Color? fillColor;
  final Color? categorytitlecolor;
  final Color? categorytbgcolor;
  final Color? inactivedotcolor;
  final Color? productplaceholdercolor;
  final Color? lightgreycolor;
  final Color? greencolor;
  final Color? greyborder;
  final Color? productrdetailbgcolor;
  final Color? shadowColor;
  final Color? dividerColor;
  final Color? darktextcolor;
  final Color? orangebordercolor;
  final Color? greycontainercolor;
  final Color? lightorangeColor;

  // Default light theme colors
  static const light = CustomColors(
    primaryColor: Color(0xffDB9F32),
    textdarkcolor: Color(0xffB4B4B4),
    hinttextcolor: Color(0Xff000000),
    fillColor: Color(0XffFFFFFF),
    categorytitlecolor: Color(0Xff828282),
    categorytbgcolor: Color(0XffFEF3F9),
    inactivedotcolor: Color(0XffD9D9D9),
    productplaceholdercolor: Color(0XffC8E8E5),
    lightgreycolor: Color(0XffA3A3A3),
    greencolor: Color(0Xff239C71),
    greyborder: Color(0Xff333333),
    productrdetailbgcolor: Color(0xffF1F0F5),
    shadowColor: Color(0xFF9B9B9B),
    dividerColor: Color(0xFFEAEAEA),
    darktextcolor: Color(0xff2E2E2E),
    orangebordercolor: Color(0xffFF9766),
    greycontainercolor: Color(0xffAEAEAE),
    lightorangeColor: Color(0xffFFBA99),
  );

  // Default dark theme colors
  static const dark = CustomColors(
    primaryColor: Color(0xff982221),
    textdarkcolor: Color(0xff5C5C5C),
    fillColor: Color(0XffFFFFFF),
    hinttextcolor: Color(0Xff000000),
    categorytitlecolor: Color(0Xff828282),
    categorytbgcolor: Color(0XffFEF3F9),
    inactivedotcolor: Color(0XffD9D9D9),
    productplaceholdercolor: Color(0XffC8E8E5),
    lightgreycolor: Color(0XffA3A3A3),
    greencolor: Color(0Xff239C71),
    greyborder: Color(0Xff333333),
    productrdetailbgcolor: Color(0xffF1F0F5),
    shadowColor: Color(0xFF9B9B9B),
    dividerColor: Color(0xFFEAEAEA),
    darktextcolor: Color(0xff2E2E2E),
    orangebordercolor: Color(0xffFF9766),
    greycontainercolor: Color(0xffAEAEAE),
    lightorangeColor: Color(0xffFFBA99),
  );

  @override
  CustomColors copyWith({
    Color? primaryColor,
    Color? textdarkcolor,
    Color? hinttextcolor,
    Color? fillColor,
    Color? categorytitlecolor,
    Color? categorytbgcolor,
    Color? inactivedotcolor,
    Color? productplaceholdercolor,
    Color? lightgreycolor,
    Color? greencolor,
    Color? greyborder,
    Color? productrdetailbgcolor,
    Color? shadowColor,
    Color? dividerColor,
    Color? darktextcolor,
    Color? orangebordercolor,
    Color? greycontainercolor,
    Color? lightorangeColor,
  }) {
    return CustomColors(
      primaryColor: primaryColor ?? this.primaryColor,
      textdarkcolor: textdarkcolor ?? this.textdarkcolor,
      hinttextcolor: hinttextcolor ?? this.hinttextcolor,
      fillColor: fillColor ?? this.fillColor,
      categorytitlecolor: categorytitlecolor ?? this.categorytitlecolor,
      categorytbgcolor: categorytbgcolor ?? this.categorytbgcolor,
      inactivedotcolor: inactivedotcolor ?? this.inactivedotcolor,
      productplaceholdercolor: productplaceholdercolor ?? this.productplaceholdercolor,
      lightgreycolor: lightgreycolor ?? this.lightgreycolor,
      greencolor: greencolor ?? this.greencolor,
      greyborder: greyborder ?? this.greyborder,
      productrdetailbgcolor: productrdetailbgcolor ?? this.productrdetailbgcolor,
      shadowColor: shadowColor ?? this.shadowColor,
      dividerColor: dividerColor ?? this.dividerColor,
      darktextcolor: darktextcolor ?? this.darktextcolor,
      orangebordercolor: orangebordercolor ?? this.orangebordercolor,
      greycontainercolor: greycontainercolor ?? this.greycontainercolor,
      lightorangeColor: lightorangeColor ?? this.lightorangeColor,
    );
  }

  @override
  CustomColors lerp(ThemeExtension<CustomColors>? other, double t) {
    if (other is! CustomColors) return this;
    return CustomColors(
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t),
      textdarkcolor: Color.lerp(textdarkcolor, other.textdarkcolor, t),
      hinttextcolor: Color.lerp(hinttextcolor, other.hinttextcolor, t),
      fillColor: Color.lerp(fillColor, other.fillColor, t),
      categorytitlecolor: Color.lerp(categorytitlecolor, other.categorytitlecolor, t),
      categorytbgcolor: Color.lerp(categorytbgcolor, other.categorytbgcolor, t),
      inactivedotcolor: Color.lerp(inactivedotcolor, other.inactivedotcolor, t),
      productplaceholdercolor: Color.lerp(productplaceholdercolor, other.productplaceholdercolor, t),
      lightgreycolor: Color.lerp(lightgreycolor, other.lightgreycolor, t),
      greencolor: Color.lerp(greencolor, other.greencolor, t),
      greyborder: Color.lerp(greyborder, other.greyborder, t),
      productrdetailbgcolor: Color.lerp(productrdetailbgcolor, other.productrdetailbgcolor, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t),
      darktextcolor: Color.lerp(darktextcolor, other.darktextcolor, t),
      orangebordercolor: Color.lerp(orangebordercolor, other.orangebordercolor, t),
      greycontainercolor: Color.lerp(greycontainercolor, other.greycontainercolor, t),
      lightorangeColor: Color.lerp(lightorangeColor, other.lightorangeColor, t),
    );
  }
}

extension ThemeDataCustomColors on ThemeData {
  CustomColors get customColors {
    final customColors = extension<CustomColors>();
    if (customColors == null) {
      return CustomColors.light;
    }
    return customColors;
  }
}
