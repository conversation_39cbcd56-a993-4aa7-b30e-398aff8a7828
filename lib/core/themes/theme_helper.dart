import 'package:zride/core/utils/app_exports.dart';

class MyAppThemeHelper {
  static TextTheme instrumentSansTextTheme(Color textColor, Color displayColor) {
    return TextTheme(
      displayLarge: TextStyle(
        fontFamily: 'InstrumentSans',
        color: displayColor,
        fontSize: 57,
        fontWeight: FontWeight.w600,
      ),
      displayMedium: TextStyle(
        fontFamily: 'InstrumentSans',
        color: displayColor,
        fontSize: 45,
        fontWeight: FontWeight.w500,
      ),
      displaySmall: TextStyle(
        fontFamily: 'InstrumentSans',
        color: displayColor,
        fontSize: 36,
        fontWeight: FontWeight.w400,
      ),
      headlineLarge: TextStyle(
        fontFamily: 'InstrumentSans',
        color: textColor,
        fontSize: 36,
        fontWeight: FontWeight.w600,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'InstrumentSans',
        color: textColor,
        fontSize: 26,
        fontWeight: FontWeight.w600,
      ),
      headlineSmall: TextStyle(
        fontFamily: 'InstrumentSans',
        color: textColor,
        fontSize: 24,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 22, fontWeight: FontWeight.w500),
      titleMedium: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 16, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 14, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 16, fontWeight: FontWeight.w400),
      bodyMedium: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 14, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 12, fontWeight: FontWeight.w400),
      labelLarge: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 14, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 12, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(fontFamily: 'InstrumentSans', color: textColor, fontSize: 11, fontWeight: FontWeight.w500),
    );
  }

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.light(
        brightness: Brightness.light,
        primary: const Color(0xff982221),
        onPrimary: const Color(0xffffffff),
        primaryContainer: ThemeData().customColors.primaryColor,
        secondary: const Color(0xff999999),
        onSecondary: const Color(0xff666666),
        secondaryContainer: const Color(0xffEBEBEB),
        error: const Color(0xffFF7B7B),
        onError: const Color(0xffFF7B7B),
        surface: const Color(0xffF8F9FA),
        onSurface: const Color(0xff7D71EC),
        outline: const Color(0xffFF5200),
      ),
      brightness: Brightness.light,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      fontFamily: 'InstrumentSans',
      textTheme: instrumentSansTextTheme(const Color(0xff000000), const Color(0xffFF5200)),
      radioTheme: RadioThemeData(
        fillColor: WidgetStatePropertyAll(Color(0xff2E2E2E)),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
      ),
      appBarTheme: const AppBarTheme(
        color: Color(0xffFF5200),
        titleTextStyle: TextStyle(
          fontFamily: 'InstrumentSans',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xffFFFFFF),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xffDEDEDE), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xffDEDEDE), width: 1),
        ),
        errorMaxLines: 2,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xffFFBA99), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Colors.redAccent, width: 2),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          disabledBackgroundColor: const Color(0xffFF5200).withValues(alpha: 0.5),
          backgroundColor: const Color(0xffFF5200),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
          textStyle: const TextStyle(fontFamily: 'InstrumentSans', fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        checkColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return Color(0xff5C5C5C);
        }),
        side: BorderSide(color: Color(0xff5C5C5C), width: 2),
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Color(0xffFF5200);
          }
          return Colors.white;
        }),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.dark(
        brightness: Brightness.dark,
        primary: const Color(0xff982221),
        onPrimary: const Color(0xffffffff),
        primaryContainer: ThemeData().customColors.primaryColor,
        secondary: const Color(0xff999999),
        onSecondary: const Color(0xff666666),
        secondaryContainer: const Color(0xffEBEBEB),
        error: const Color(0xffFF7B7B),
        onError: const Color(0xffFF7B7B),
        surface: const Color(0xffF8F9FA),
        onSurface: const Color(0xff7D71EC),
        outline: const Color(0xffFF5200),
      ),
      brightness: Brightness.dark,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      fontFamily: 'InstrumentSans',
      textTheme: instrumentSansTextTheme(const Color(0xffFFFFFF), const Color(0xffFF5200)),
      radioTheme: RadioThemeData(
        fillColor: WidgetStatePropertyAll(Color(0xff2E2E2E)),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
      ),
      appBarTheme: const AppBarTheme(
        color: Color(0xff982221),
        titleTextStyle: TextStyle(
          fontFamily: 'InstrumentSans',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xffFFFFFF),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xff666666), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xff666666), width: 1),
        ),
        errorMaxLines: 2,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xff666666), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.0),
          borderSide: const BorderSide(color: Color(0xff982221), width: 2),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          disabledBackgroundColor: const Color(0xff982221).withValues(alpha: 0.5),
          backgroundColor: const Color(0xff982221),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
          textStyle: const TextStyle(fontFamily: 'InstrumentSans', fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        checkColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return Color(0xff5C5C5C);
        }),
        side: BorderSide(color: Color(0xff5C5C5C), width: 2),
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Color(0xffFF5200);
          }
          return Colors.white;
        }),
      ),
    );
  }
}
