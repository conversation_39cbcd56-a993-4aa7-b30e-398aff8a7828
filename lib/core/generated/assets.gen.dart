// dart format width=160

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/pngs
  $AssetsImagesPngsGen get pngs => const $AssetsImagesPngsGen();

  /// Directory path: assets/images/svgs
  $AssetsImagesSvgsGen get svgs => const $AssetsImagesSvgsGen();
}

class $AssetsImagesPngsGen {
  const $AssetsImagesPngsGen();

  /// Directory path: assets/images/pngs/authentication
  $AssetsImagesPngsAuthenticationGen get authentication => const $AssetsImagesPngsAuthenticationGen();

  /// Directory path: assets/images/pngs/login
  $AssetsImagesPngsLoginGen get login => const $AssetsImagesPngsLoginGen();

  /// Directory path: assets/images/pngs/onboarding
  $AssetsImagesPngsOnboardingGen get onboarding => const $AssetsImagesPngsOnboardingGen();

  /// Directory path: assets/images/pngs/other
  $AssetsImagesPngsOtherGen get other => const $AssetsImagesPngsOtherGen();
}

class $AssetsImagesSvgsGen {
  const $AssetsImagesSvgsGen();

  /// Directory path: assets/images/svgs/icons
  $AssetsImagesSvgsIconsGen get icons => const $AssetsImagesSvgsIconsGen();
}

class $AssetsImagesPngsAuthenticationGen {
  const $AssetsImagesPngsAuthenticationGen();

  /// File path: assets/images/pngs/authentication/png_splash_bg.png
  AssetGenImage get pngSplashBg => const AssetGenImage('assets/images/pngs/authentication/png_splash_bg.png');

  /// File path: assets/images/pngs/authentication/png_zride_logo.png
  AssetGenImage get pngZrideLogo => const AssetGenImage('assets/images/pngs/authentication/png_zride_logo.png');

  /// List of all assets
  List<AssetGenImage> get values => [pngSplashBg, pngZrideLogo];
}

class $AssetsImagesPngsLoginGen {
  const $AssetsImagesPngsLoginGen();

  /// File path: assets/images/pngs/login/png_login_bg.png
  AssetGenImage get pngLoginBg => const AssetGenImage('assets/images/pngs/login/png_login_bg.png');

  /// File path: assets/images/pngs/login/png_permission_bg.png
  AssetGenImage get pngPermissionBg => const AssetGenImage('assets/images/pngs/login/png_permission_bg.png');

  /// List of all assets
  List<AssetGenImage> get values => [pngLoginBg, pngPermissionBg];
}

class $AssetsImagesPngsOnboardingGen {
  const $AssetsImagesPngsOnboardingGen();

  /// File path: assets/images/pngs/onboarding/png_icon_onbording_arrow.png
  AssetGenImage get pngIconOnbordingArrow => const AssetGenImage('assets/images/pngs/onboarding/png_icon_onbording_arrow.png');

  /// File path: assets/images/pngs/onboarding/png_onbording_bg1.png
  AssetGenImage get pngOnbordingBg1 => const AssetGenImage('assets/images/pngs/onboarding/png_onbording_bg1.png');

  /// File path: assets/images/pngs/onboarding/png_onbording_bg2.png
  AssetGenImage get pngOnbordingBg2 => const AssetGenImage('assets/images/pngs/onboarding/png_onbording_bg2.png');

  /// File path: assets/images/pngs/onboarding/png_onbording_bg3.png
  AssetGenImage get pngOnbordingBg3 => const AssetGenImage('assets/images/pngs/onboarding/png_onbording_bg3.png');

  /// File path: assets/images/pngs/onboarding/png_skip.png
  AssetGenImage get pngSkip => const AssetGenImage('assets/images/pngs/onboarding/png_skip.png');

  /// List of all assets
  List<AssetGenImage> get values => [pngIconOnbordingArrow, pngOnbordingBg1, pngOnbordingBg2, pngOnbordingBg3, pngSkip];
}

class $AssetsImagesPngsOtherGen {
  const $AssetsImagesPngsOtherGen();

  /// File path: assets/images/pngs/other/png_car.png
  AssetGenImage get pngCar => const AssetGenImage('assets/images/pngs/other/png_car.png');

  /// File path: assets/images/pngs/other/png_ic_home.png
  AssetGenImage get pngIcHome => const AssetGenImage('assets/images/pngs/other/png_ic_home.png');

  /// File path: assets/images/pngs/other/png_location.png
  AssetGenImage get pngLocation => const AssetGenImage('assets/images/pngs/other/png_location.png');

  /// File path: assets/images/pngs/other/png_profile.png
  AssetGenImage get pngProfile => const AssetGenImage('assets/images/pngs/other/png_profile.png');

  /// List of all assets
  List<AssetGenImage> get values => [pngCar, pngIcHome, pngLocation, pngProfile];
}

class $AssetsImagesSvgsIconsGen {
  const $AssetsImagesSvgsIconsGen();

  /// File path: assets/images/svgs/icons/svg_ic_back_arrow.svg
  SvgGenImage get svgIcBackArrow => const SvgGenImage('assets/images/svgs/icons/svg_ic_back_arrow.svg');

  /// File path: assets/images/svgs/icons/svg_ic_calander.svg
  SvgGenImage get svgIcCalander => const SvgGenImage('assets/images/svgs/icons/svg_ic_calander.svg');

  /// File path: assets/images/svgs/icons/svg_ic_call.svg
  SvgGenImage get svgIcCall => const SvgGenImage('assets/images/svgs/icons/svg_ic_call.svg');

  /// File path: assets/images/svgs/icons/svg_ic_camera.svg
  SvgGenImage get svgIcCamera => const SvgGenImage('assets/images/svgs/icons/svg_ic_camera.svg');

  /// File path: assets/images/svgs/icons/svg_ic_car.svg
  SvgGenImage get svgIcCar => const SvgGenImage('assets/images/svgs/icons/svg_ic_car.svg');

  /// File path: assets/images/svgs/icons/svg_ic_contact.svg
  SvgGenImage get svgIcContact => const SvgGenImage('assets/images/svgs/icons/svg_ic_contact.svg');

  /// File path: assets/images/svgs/icons/svg_ic_credentials.svg
  SvgGenImage get svgIcCredentials => const SvgGenImage('assets/images/svgs/icons/svg_ic_credentials.svg');

  /// File path: assets/images/svgs/icons/svg_ic_email.svg
  SvgGenImage get svgIcEmail => const SvgGenImage('assets/images/svgs/icons/svg_ic_email.svg');

  /// File path: assets/images/svgs/icons/svg_ic_gender.svg
  SvgGenImage get svgIcGender => const SvgGenImage('assets/images/svgs/icons/svg_ic_gender.svg');

  /// File path: assets/images/svgs/icons/svg_ic_home.svg
  SvgGenImage get svgIcHome => const SvgGenImage('assets/images/svgs/icons/svg_ic_home.svg');

  /// File path: assets/images/svgs/icons/svg_ic_location.svg
  SvgGenImage get svgIcLocation => const SvgGenImage('assets/images/svgs/icons/svg_ic_location.svg');

  /// File path: assets/images/svgs/icons/svg_ic_notification.svg
  SvgGenImage get svgIcNotification => const SvgGenImage('assets/images/svgs/icons/svg_ic_notification.svg');

  /// File path: assets/images/svgs/icons/svg_ic_password.svg
  SvgGenImage get svgIcPassword => const SvgGenImage('assets/images/svgs/icons/svg_ic_password.svg');

  /// File path: assets/images/svgs/icons/svg_ic_people.svg
  SvgGenImage get svgIcPeople => const SvgGenImage('assets/images/svgs/icons/svg_ic_people.svg');

  /// File path: assets/images/svgs/icons/svg_ic_ride.svg
  SvgGenImage get svgIcRide => const SvgGenImage('assets/images/svgs/icons/svg_ic_ride.svg');

  /// File path: assets/images/svgs/icons/svg_ic_right_arrow.svg
  SvgGenImage get svgIcRightArrow => const SvgGenImage('assets/images/svgs/icons/svg_ic_right_arrow.svg');

  /// File path: assets/images/svgs/icons/svg_ic_right_arrow_color.svg
  SvgGenImage get svgIcRightArrowColor => const SvgGenImage('assets/images/svgs/icons/svg_ic_right_arrow_color.svg');

  /// File path: assets/images/svgs/icons/svg_ic_setting.svg
  SvgGenImage get svgIcSetting => const SvgGenImage('assets/images/svgs/icons/svg_ic_setting.svg');

  /// File path: assets/images/svgs/icons/svg_ic_swip.svg
  SvgGenImage get svgIcSwip => const SvgGenImage('assets/images/svgs/icons/svg_ic_swip.svg');

  /// File path: assets/images/svgs/icons/svg_ic_timer.svg
  SvgGenImage get svgIcTimer => const SvgGenImage('assets/images/svgs/icons/svg_ic_timer.svg');

  /// File path: assets/images/svgs/icons/svg_ic_wallet.svg
  SvgGenImage get svgIcWallet => const SvgGenImage('assets/images/svgs/icons/svg_ic_wallet.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    svgIcBackArrow,
    svgIcCalander,
    svgIcCall,
    svgIcCamera,
    svgIcCar,
    svgIcContact,
    svgIcCredentials,
    svgIcEmail,
    svgIcGender,
    svgIcHome,
    svgIcLocation,
    svgIcNotification,
    svgIcPassword,
    svgIcPeople,
    svgIcRide,
    svgIcRightArrow,
    svgIcRightArrowColor,
    svgIcSetting,
    svgIcSwip,
    svgIcTimer,
    svgIcWallet,
  ];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env';
  static const $AssetsImagesGen images = $AssetsImagesGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}, this.animation});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({required this.isAnimation, required this.duration, required this.frames});

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(_assetName, assetBundle: bundle, packageName: package, theme: theme, colorMapper: colorMapper);
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
