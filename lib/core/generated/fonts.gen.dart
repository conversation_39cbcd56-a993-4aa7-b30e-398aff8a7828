// dart format width=160
/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

class FontFamily {
  FontFamily._();

  /// Font family: InstrumentSans
  static const String instrumentSans = 'InstrumentSans';
}
