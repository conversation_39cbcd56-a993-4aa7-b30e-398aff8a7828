// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class Lang {
  Lang();

  static Lang? _current;

  static Lang get current {
    assert(
      _current != null,
      'No instance of <PERSON> was loaded. Try to initialize the Lang delegate before accessing Lang.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<Lang> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = Lang();
      Lang._current = instance;

      return instance;
    });
  }

  static Lang of(BuildContext context) {
    final instance = Lang.maybeOf(context);
    assert(
      instance != null,
      'No instance of Lang present in the widget tree. Did you add Lang.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static Lang? maybeOf(BuildContext context) {
    return Localizations.of<Lang>(context, Lang);
  }

  /// `ZRide`
  String get lbl_app_name {
    return Intl.message('ZRide', name: 'lbl_app_name', desc: '', args: []);
  }

  /// `Cancel`
  String get lbl_Cancel {
    return Intl.message('Cancel', name: 'lbl_Cancel', desc: '', args: []);
  }

  /// `Please enter phone number`
  String get lbl_emptyPhoneNumber {
    return Intl.message(
      'Please enter phone number',
      name: 'lbl_emptyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Phone Number`
  String get lbl_invalidPhoneNumber {
    return Intl.message(
      'Invalid Phone Number',
      name: 'lbl_invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please enter verification code`
  String get lbl_emptyVerificationCode {
    return Intl.message(
      'Please enter verification code',
      name: 'lbl_emptyVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Name should not be empty`
  String get lbl_emptyName {
    return Intl.message(
      'Name should not be empty',
      name: 'lbl_emptyName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter password`
  String get lbl_emptyPassword {
    return Intl.message(
      'Please enter password',
      name: 'lbl_emptyPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please enter confirm password`
  String get lbl_emptyConfirmPassword {
    return Intl.message(
      'Please enter confirm password',
      name: 'lbl_emptyConfirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match`
  String get lbl_passwordMismatch {
    return Intl.message(
      'Passwords do not match',
      name: 'lbl_passwordMismatch',
      desc: '',
      args: [],
    );
  }

  /// `Please enter an email address`
  String get lbl_emptyEmail {
    return Intl.message(
      'Please enter an email address',
      name: 'lbl_emptyEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address`
  String get lbl_invalidEmail {
    return Intl.message(
      'Please enter a valid email address',
      name: 'lbl_invalidEmail',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<Lang> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[Locale.fromSubtags(languageCode: 'en')];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<Lang> load(Locale locale) => Lang.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
