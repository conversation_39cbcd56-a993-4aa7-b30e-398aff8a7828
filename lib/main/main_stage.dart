
import 'package:zride/core/utils/app_exports.dart';

Future<void> main() async {
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    Logger.lOG("Error loading .env file: $e");
  }
  WidgetsFlutterBinding.ensureInitialized();
  // Custom error widget
  ErrorWidget.builder = (FlutterErrorDetails details) {
    return CustomErrorWidget(errorMessage: details.exception.toString());
  };
  // Get device info
  

  EnvConfig stageConfig = EnvConfig(
    baseUrl: STAGE_SERVER_BASEURL,
  );
  // Env config
  FlavorConfig.initialize(flavor: Flavor.stage, env: stageConfig);

  Logger.lOG(
    "FLAVOR             : ${FlavorConfig.instance.flavor}\n"
    "BASE URL           : $STAGE_SERVER_BASEURL\n"
  );

  Logger.lOG("STAGE ENV: ${FlavorConfig.instance.flavor}");

  try {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocDir.path);
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await Hive.openBox('ZRide').then((value) => runApp(ZrideApp(prefs: value)));
  } catch (e, stackTrace) {
    Logger.lOG('Error initializing app: $e\n$stackTrace');
  }
}
