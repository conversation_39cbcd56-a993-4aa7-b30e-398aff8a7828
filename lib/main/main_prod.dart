

import 'package:zride/core/utils/app_exports.dart';

Future<void> main() async {
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    Logger.lOG("Error loading .env file: $e");
  }
  WidgetsFlutterBinding.ensureInitialized();

  // Custom error widget
  ErrorWidget.builder = (FlutterErrorDetails details) {
    return CustomErrorWidget(errorMessage: details.exception.toString());
  };
  // Get device info
  

  EnvConfig prodConfig = EnvConfig(
    baseUrl: PROD_SERVER_BASEURL,
  );
  // Env config
  FlavorConfig.initialize(flavor: Flavor.prod, env: prodConfig);

  Logger.lOG(
    "FLAVOR             : ${FlavorConfig.instance.flavor}\n"
    "BASE URL           : $PROD_SERVER_BASEURL\n"
  );

  try {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocDir.path);
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await Hive.openBox('ZRide').then((value) => runApp(ZrideApp(prefs: value)));
  } catch (e, stackTrace) {
    Logger.lOG('Error initializing app: $e\n$stackTrace');
  }
}
